defmodule OrdersService.Refunds do
  @moduledoc """
  The Refunds context.
  """
  import Ecto.Query, warn: false

  alias Adyen.Services.Checkout
  alias Ecto.Multi
  alias ExServiceClient.Services.EventsService
  alias OrdersService.Auth
  alias OrdersService.Bill
  alias OrdersService.Config
  alias OrdersService.Order
  alias OrdersService.OrderHistory
  alias OrdersService.OrderTicket
  alias OrdersService.PayinTransaction
  alias OrdersService.PersonalInformation
  alias OrdersService.Pubsub.Publisher.OrdersPublisher
  alias OrdersService.Refunds.RefundTransaction
  alias OrdersService.Repo
  alias OrdersService.Ticket, as: TicketDB
  alias OrdersService.TicketHistory
  alias OrdersService.Tickets.Ticket
  alias OrdersService.Tickets.TicketValidator
  alias OrdersService.TransactionHistory

  require Logger

  @currency "EUR"

  @spec refund_tickets(map(), user_id :: Ecto.UUID.t(), conn :: Plug.Conn.t()) ::
          {:ok, [TicketDB.t()]} | {:error, any()} | {:error, :refund, any()}
  def refund_tickets(%{"ticket_ids" => ticket_ids, "reason" => reason} = params, user_id, conn) do
    params = Map.put(params, "user_id", user_id)

    with {:tickets, {:ok, tickets}} <- {:tickets, TicketDB.get_tickets_by_id(ticket_ids)},
         {:validate_event_access, :ok} <-
           {:validate_event_access, validate_event_access(tickets, conn)},
         {:validate_ticket_refundable, :ok} <-
           {:validate_ticket_refundable, validate_multiple_ticket_refundable(tickets)},
         {:prepare_refund_payload, {:ok, refundable_ticket_payloads}} <-
           {:prepare_refund_payload, prepare_multiple_refund_payload(tickets, reason)},
         {:refund_multiple_tickets, :ok} <-
           {:refund_multiple_tickets, refund_multiple_tickets(refundable_ticket_payloads, params)},
         {:publish_order_updates, :ok} <- {:publish_order_updates, process_order_updates(tickets)} do
      {:ok, tickets}
    else
      {:tickets, {:error, errors}} ->
        Logger.error(
          "Refund validation failed because all tickets could not be retrieved with error #{inspect(errors)}"
        )

        {:error, :tickets}

      {:validate_event_access, _} ->
        Logger.error("Can't refund tickets because of unauthorized event access")
        {:error, :access}

      {:validate_ticket_refundable, error} ->
        Logger.error("Can't refund tickets because of #{inspect(error)}  some tickets are not refundable")

        {:error, :ticket_refundable}

      {:refund_multiple_tickets, error} ->
        Logger.error("Can't refund tickets because of #{inspect(error)} some tickets could not be refunded")

        {:error, :refund, error}

      {:prepare_refund_payload, {:error, :insufficient_funds}} ->
        Logger.error("Can't refund tickets because of insufficient funds")
        {:error, :insufficient_funds}

      {:publish_order_updates, error} ->
        Logger.error(
          "Can't publish order updates for affected orders of ticket_ids #{inspect(ticket_ids)} due to error: #{inspect(error)}"
        )

        {:error, :order_update_publish_failed}
    end
  end

  @spec calculate_refunds_split(
          bill :: Bill.t(),
          ticket :: TicketDB.t(),
          balance_account_id :: String.t(),
          config :: map()
        ) :: [map()]
  def calculate_refunds_split(%Bill{} = bill, %TicketDB{id: reference} = _ticket, balance_account_id, config) do
    %Bill{
      presale_fee: presale_fee,
      presale_fee_tax: presale_fee_tax,
      system_fee: system_fee,
      system_fee_tax: system_fee_tax,
      promoter_kickback: kickback_sum,
      promoter_kickback_tax: promoter_kickback_tax,
      promoter_total: promoter_total,
      future_demand_fee: future_demand_fee,
      future_demand_fee_tax: future_demand_fee_tax,
      donation: donation
    } = bill

    %{
      "presaleFeeAdyenBalanceAccountId" => presale_fee_adyen_balance_account_id,
      "transactionFeeAdyenBalanceAccountId" => transaction_fee_adyen_balance_account_id,
      "defaultDonationBalanceAccountId" => default_donation_balance_account_id,
      "futureDemandFeeAdyenBalanceAccountId" => future_demand_fee_adyen_balance_account_id
    } = config

    splits = []

    splits =
      if presale_fee == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => presale_fee_adyen_balance_account_id,
            "reference" => "presale-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => presale_fee + presale_fee_tax
            }
          }
          | splits
        ]
      end

    splits =
      if system_fee == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => transaction_fee_adyen_balance_account_id,
            "reference" => "transaction-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => system_fee + system_fee_tax
            }
          }
          | splits
        ]
      end

    splits =
      if kickback_sum == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => balance_account_id,
            "reference" => "kickback-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => kickback_sum + promoter_kickback_tax
            }
          }
          | splits
        ]
      end

    splits =
      if promoter_total == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => balance_account_id,
            "reference" => "promoter-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => promoter_total
            }
          }
          | splits
        ]
      end

    splits =
      if donation == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => default_donation_balance_account_id,
            "reference" => "donation-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => donation
            }
          }
          | splits
        ]
      end

    splits =
      if future_demand_fee == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => future_demand_fee_adyen_balance_account_id,
            "reference" => "future_demand-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => future_demand_fee + future_demand_fee_tax
            }
          }
          | splits
        ]
      end

    splits
  end

  @doc """
  Validates if the user associated with the connection has access to all events
  linked to the provided tickets.

  Access is granted if, for every ticket:
  1. The user is the seller associated with the ticket's order, OR
  2. The user has the "event.ticket.checkin" permission for the ticket's event.

  Returns `:ok` if access is granted for all tickets, otherwise `{:error, :unauthorized}`.
  """
  @spec validate_event_access(tickets :: [TicketDB.t()], conn :: Plug.Conn.t()) ::
          :ok | {:error, :unauthorized}
  def validate_event_access(tickets, conn) do
    conn_seller_id = Auth.get_seller_id_from_token(conn)

    all_accessible? =
      Enum.all?(tickets, fn ticket ->
        ticket_accessible?(ticket, conn_seller_id, conn)
      end)

    if all_accessible? do
      :ok
    else
      {:error, :unauthorized}
    end
  end

  @spec refund_multiple_tickets(tickets :: [{map(), String.t(), TicketDB.t()}], params :: map()) ::
          :ok | {:error, list()}
  def refund_multiple_tickets(refundable_ticket_payloads, params) do
    Logger.debug("Refund multiple tickets with payloads #{inspect(refundable_ticket_payloads)}")

    refundable_ticket_payloads
    |> Enum.reduce_while(:ok, fn {payload, psp_reference, ticket}, _acc ->
      case refund_ticket(payload, psp_reference, ticket, params) do
        :ok -> {:cont, :ok}
        {:error, error} -> {:halt, error}
      end
    end)
    |> case do
      :ok -> :ok
      error -> {:error, error}
    end
  end

  @spec prepare_multiple_refund_payload(tickets :: [TicketDB.t()], reason :: String.t()) ::
          {:ok, [{map(), String.t(), TicketDB.t()}]} | {:error, atom()}
  def prepare_multiple_refund_payload(tickets, reason) do
    tickets
    |> Enum.reduce_while({:ok, []}, fn ticket, {_error, refundable_ticket_payloads} ->
      case prepare_ticket_refund_payload(ticket, reason) do
        {:ok, payload} -> {:cont, {:ok, [payload | refundable_ticket_payloads]}}
        {:error, error} -> {:halt, {error, refundable_ticket_payloads}}
      end
    end)
    |> case do
      {:ok, refundable_ticket_payloads} -> {:ok, refundable_ticket_payloads}
      {error, _} -> {:error, error}
    end
  end

  @spec validate_multiple_ticket_refundable(tickets :: [TicketDB.t()]) :: :ok | {:error, list()}
  def validate_multiple_ticket_refundable(tickets) do
    tickets
    |> Enum.reduce_while(:ok, fn item, error ->
      case TicketValidator.validate_ticket_refundable(item) do
        :ok -> {:cont, error}
        {:error, item} -> {:halt, item}
      end
    end)
    |> case do
      :ok -> :ok
      error -> {:error, error}
    end
  end

  @spec prepare_ticket_refund_payload(ticket :: TicketDB.t(), reason :: String.t()) ::
          {:ok, {map(), String.t(), TicketDB.t()}} | {:error, atom()}
  def prepare_ticket_refund_payload(%TicketDB{} = ticket, reason) do
    %TicketDB{
      id: ticket_id,
      order_ticket: %OrderTicket{order_id: order_id, bill_id: bill_id},
      event_id: event_id
    } =
      ticket

    with {:bill, %Bill{total: bill_total} = bill} <- {:bill, Repo.get(Bill, bill_id)},
         {:payin_transaction,
          {%PayinTransaction{psp_reference: psp_reference, payment_method: payment_method}, _bill_total}} <-
           {:payin_transaction, {PayinTransaction.get_by_order_id(order_id), bill_total}},
         {:refund_balance_account, balance_account_id} when not is_nil(balance_account_id) <-
           {:refund_balance_account, get_refund_balance_account(String.to_existing_atom(reason), event_id)},
         {:validate_fund_availability, true} <-
           {:validate_fund_availability, validate_fund_availability(bill_total, balance_account_id)} do
      config = Config.all_as_map()
      splits = calculate_refunds_split(bill, ticket, balance_account_id, config)

      adyen_payload = %{
        amount: %{value: bill_total, currency: "EUR"},
        reference: ticket_id,
        splits: splits,
        payment_method: payment_method,
        merchant_refund_reason: "CUSTOMER REQUEST"
      }

      {:ok, {adyen_payload, psp_reference, ticket}}
    else
      {:bill, _} ->
        Logger.error("Can't initiate refund for ticket #{ticket_id} because of missing bill")
        {:error, :missing_bill}

      {:payin_transaction, {nil, bill_total}} ->
        Logger.debug(
          "Invitation or Seller order #{order_id} has no payin transaction. Ticket #{ticket_id} will be set to REFUNDED"
        )

        {:ok, {%{amount: %{value: bill_total, currency: "EUR"}, reference: ticket_id}, nil, ticket}}

      {:refund_balance_account, nil} ->
        Logger.error("Can't initiate refund for ticket #{ticket_id} because of missing balance account")

        {:error, :missing_balance_account}

      {:validate_fund_availability, false} ->
        Logger.error("Can't initiate refund for ticket #{ticket_id} because of insufficient funds")

        {:error, :insufficient_funds}
    end
  end

  @doc """
   Guest list ticket do not have a payment transaction and thus no psp_reference.
  """
  @spec refund_ticket(
          attrs :: map(),
          psp_reference :: String.t() | nil,
          ticket :: TicketDB.t(),
          params :: map()
        ) ::
          :ok | {:error, any()}
  def refund_ticket(
        %{amount: amount} = _payload,
        nil,
        %TicketDB{id: ticket_id, order_ticket: %OrderTicket{order_id: order_id}} = ticket,
        %{"user_id" => user_id, "reason" => reason} = _params
      ) do
    refund_transaction_payload =
      create_refund_transaction_payload(amount, ticket, user_id, reason)

    with {:refund_transaction, {:ok, _refund_transaction}} <-
           {:refund_transaction, create_refund_transaction(refund_transaction_payload)},
         {:refund, {:ok, _updated_ticket}} <-
           {:refund, TicketDB.set_status(ticket, :REFUNDED, user_id, nil, nil, true, %{})},
         {:refund_order, :ok} <- {:refund_order, maybe_set_order_as_refunded(order_id)} do
      Logger.debug("Set ticket #{inspect(ticket_id)} to REFUNDED")
      :ok
    else
      {:refund_transaction, {operation, failed_value}} ->
        Logger.error("Can't create refund transaction for ticket #{ticket_id} because of #{inspect(operation)}
#{inspect(failed_value)}")

        {:error, failed_value}

      {:refund, error} ->
        Logger.error("Can't set ticket #{inspect(ticket_id)} to REFUNDED because of #{inspect(error)}")

        {:error, error}

      {:refund_order, {:error, error}} ->
        Logger.error("Can't set order #{inspect(order_id)} to REFUNDED because of #{inspect(error)}")

        {:error, error}
    end
  end

  def refund_ticket(payload, psp_reference, %TicketDB{id: ticket_id} = ticket, %{"user_id" => user_id} = params) do
    Logger.debug(
      "Refund ticket #{ticket_id} with payload #{inspect(payload)} and psp_reference #{inspect(psp_reference)}"
    )

    with {:process_refund, {:ok, response}} <-
           {:process_refund, Checkout.refunds(payload, psp_reference)},
         {:refund_transaction_payload, refund_transaction_payload} <-
           {:refund_transaction_payload,
            create_refund_transaction_payload(
              payload,
              {user_id, ticket_id, psp_reference},
              params,
              response
            )},
         {:create_refund_transaction, {:ok, _refund_transaction}} <-
           {:create_refund_transaction, create_refund_transaction(refund_transaction_payload)},
         {:update_status, {:ok, _updated_ticket}} <-
           {:update_status, TicketDB.set_status(ticket, :REFUNDING, user_id, nil, nil, false, response)} do
      Logger.debug("Refund ticket #{ticket_id} with response #{inspect(response)}")
      :ok
    else
      {:update_status, error} ->
        Logger.error("Can't refund ticket #{ticket_id} because of status change error #{inspect(error)}")

        {:error, error}

      {:process_refund, {:error, error}} ->
        Logger.error("Can't refund ticket #{ticket_id} because of refund error #{inspect(error)}")
        {:error, error}

      {:create_refund_transaction, {operation, failed_value}} ->
        Logger.error("Can't create refund transaction for ticket #{ticket_id} because of #{inspect(operation)}")

        {:error, failed_value}
    end
  end

  @doc """
   Finalize ticket refund is a function that is called while processing a refund ticket webhook.
   Since Adyen requires a webhook to be processed within 5 seconds and always return a 200 status code, further error handling
   is not possible in this function. If an error occurs, it is logged and the error is returned as a tuple.
  """
  @spec revert_refund(refund_transaction :: RefundTransaction.t(), item :: map()) ::
          atom() | {:error, any()}
  def revert_refund(
        %RefundTransaction{id: refund_transaction_id, refund_item_id: refund_item_id, refund_item_type: :ORDER} =
          refund_transaction,
        item
      ) do
    order = Order.get(refund_item_id)

    Multi.new()
    |> Multi.insert(
      :insert_transaction_history,
      TransactionHistory.changeset(%TransactionHistory{}, %{
        "state" => "FAILED",
        "transaction_id" => refund_transaction_id
      })
    )
    |> Multi.update(:update_order, Order.changeset(order, %{status: :TIMEDOUT}))
    |> Multi.run(:insert_order_history, fn _repo, _changes ->
      OrderHistory.create(order, :TIMEDOUT)
    end)
    |> Multi.update(
      :update_refund_transaction,
      RefundTransaction.changeset(refund_transaction, %{status: "FAILED", psp_result: item})
    )
    |> Repo.transaction()
    |> case do
      {:ok, _transaction_data} -> :ok
      {:error, operation, failed_value, _changes_so_far} -> {:error, {operation, failed_value}}
    end
  end

  def revert_refund(
        %RefundTransaction{refund_item_id: refund_item_id, refund_item_type: :TICKET} = refund_transaction,
        item
      ) do
    with {:ticket,
          %TicketDB{
            id: _ticket_id,
            status: current_status,
            order_ticket: %OrderTicket{order_id: order_id}
          } = ticket} <-
           {:ticket, TicketDB.get(refund_item_id)},
         {:status_change, true} <-
           {:status_change, TicketDB.status_change_allowed?(current_status, :ACTIVE)},
         {:revert_transaction, {:ok, updated_ticket}} <-
           {:revert_transaction, revert_refund_transaction(ticket, refund_transaction, item)} do
      order_id
      |> Order.get()
      |> TicketDB.maybe_revert_order_status()
      |> case do
        :ok ->
          Ticket.update_tickets_counter(updated_ticket)
          :ok

        {:error, error} ->
          Logger.error("Can't revert order status for order #{order_id} because of #{inspect(error)}")

          {:error, error}
      end
    else
      error -> {:error, error}
    end
  end

  @doc """
   Finalize ticket refund is a function that is called while processing a refund ticket webhook.
   Since Adyen requires a webhook to be processed within 5 seconds and always return a 200 status code, further error handling
   is not possible in this function. If an error occurs, it is logged and the error is returned as a tuple.
  """
  @spec finalize_ticket_refund(ticket :: TicketDB.t(), item :: map()) :: atom() | {:error, any()}
  def finalize_ticket_refund(%TicketDB{} = ticket, item) do
    %TicketDB{
      id: ticket_id,
      status: current_status,
      order_ticket: %OrderTicket{order_id: order_id}
    } = ticket

    with {:status_change, true} <-
           {:status_change, TicketDB.status_change_allowed?(current_status, :REFUNDED)},
         {:refund_transaction, %RefundTransaction{} = refund_transaction} <-
           {:refund_transaction, get_refund_transaction_by_type(ticket_id, :TICKET)},
         {:finalize_transaction, {:ok, updated_ticket}} <-
           {:finalize_transaction, finalize_refund_transaction(ticket, refund_transaction, item)} do
      order_id
      |> TicketDB.count_not_refunded_tickets_for_order_by_order_id()
      |> TicketDB.maybe_set_order_as_refunded(order_id)
      |> case do
        :ok ->
          Ticket.update_tickets_counter(updated_ticket)

        {:error, error} ->
          Logger.error("Can't set order as refunded for order #{order_id} because of #{inspect(error)}")

          {:error, error}
      end
    else
      error -> {:error, error}
    end
  end

  @spec list_refund_transactions(params :: map()) :: Scrivener.Page.t()
  def list_refund_transactions(params) do
    query =
      from(rt in RefundTransaction,
        as: :refund_transaction,
        inner_join: t in TicketDB,
        as: :ticket,
        on: rt.refund_item_id == t.id and rt.refund_item_type == :TICKET,
        inner_join: pi in PersonalInformation,
        as: :personal_information,
        on: t.attendee_id == pi.id,
        inner_join: ot in OrderTicket,
        as: :order_ticket,
        on: ot.ticket_id == t.id,
        left_join: o in Order,
        as: :order,
        on: ot.order_id == o.id
      )

    query
    |> where(^filter_where_params(params))
    |> Repo.paginate(params)
  end

  @spec list_refund_transactions_by_ticket_ids(ticket_ids :: [Ecto.UUID.t()]) :: [map()]
  def list_refund_transactions_by_ticket_ids(ticket_ids) do
    RefundTransaction
    |> join(:inner, [rt], t in TicketDB, on: rt.refund_item_id == t.id)
    |> join(:inner, [rt, t], pi in PersonalInformation, on: t.attendee_id == pi.id)
    |> where([rt, t, pi], t.id in ^ticket_ids and rt.refund_item_type == :TICKET)
    |> select([rt, t, pi], %{refund: rt, ticket: t, ticket_personal_information: pi})
    |> Repo.all()
  end

  @spec create_refund_transaction(attrs :: map()) ::
          {:ok, RefundTransaction.t()} | {:error, {atom(), any()}}
  def create_refund_transaction(attrs \\ %{}) do
    Multi.new()
    |> Multi.insert(
      :insert_refund_transaction,
      RefundTransaction.changeset(%RefundTransaction{}, attrs)
    )
    |> Multi.insert(:insert_transaction_history, fn %{
                                                      insert_refund_transaction: refund_transaction
                                                    } ->
      TransactionHistory.changeset(
        %TransactionHistory{},
        %{
          "state" => Atom.to_string(refund_transaction.status),
          "transaction_id" => refund_transaction.id
        }
      )
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{insert_refund_transaction: refund_transaction}} -> {:ok, refund_transaction}
      {:error, operation, failed_value, _changes_so_far} -> {:error, {operation, failed_value}}
    end
  end

  @spec merchant_reference_valid_uuid?(merchant_reference :: String.t()) :: boolean()
  def merchant_reference_valid_uuid?(merchant_reference) do
    case Ecto.UUID.dump(merchant_reference) do
      {:ok, _} -> true
      _ -> false
    end
  end

  @spec get_refund_transaction(refund_transaction_id :: Ecto.UUID.t()) ::
          RefundTransaction.t() | nil
  def get_refund_transaction(refund_transaction_id) do
    RefundTransaction
    |> where([r], r.id == ^refund_transaction_id)
    |> Repo.one()
  end

  @spec get_refund_transaction_by_type(
          refund_item_id :: Ecto.UUID.t(),
          refund_item_type :: atom()
        ) ::
          RefundTransaction.t() | nil
  def get_refund_transaction_by_type(refund_item_id, refund_item_type \\ :TICKET) do
    RefundTransaction
    |> where([r], r.refund_item_id == ^refund_item_id and r.refund_item_type == ^refund_item_type)
    |> Repo.one()
  end

  @spec get_refund_transaction_by(opts :: Keyword.t()) :: RefundTransaction.t() | nil
  def get_refund_transaction_by(opts) do
    Repo.get_by(RefundTransaction, opts)
  end

  @spec finalize_refund_transaction(
          ticket :: TicketDB.t(),
          refund_transaction :: RefundTransaction.t(),
          psp_response :: map()
        ) ::
          {:ok, TicketDB.t()} | {:error, {atom(), any()}}
  def finalize_refund_transaction(
        %TicketDB{} = ticket,
        %RefundTransaction{actor_id: actor_id} = refund_transaction,
        psp_response
      ) do
    Multi.new()
    |> Multi.run(:update_ticket, fn _repo, _changes ->
      TicketDB.set_status(ticket, :REFUNDED, actor_id, nil, nil, true, psp_response)
    end)
    |> Multi.insert(
      :insert_transaction_history,
      TransactionHistory.changeset(%TransactionHistory{}, %{
        "state" => "REFUNDED",
        "transaction_id" => refund_transaction.id
      })
    )
    |> Multi.update(
      :update_refund_transaction,
      RefundTransaction.changeset(refund_transaction, %{
        status: "COMPLETED",
        psp_result: psp_response
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update_ticket: updated_ticket}} -> {:ok, updated_ticket}
      {:error, operation, failed_value, _changes_so_far} -> {:error, {operation, failed_value}}
    end
  end

  def finalize_refund_transaction(%Order{} = order, %RefundTransaction{} = refund_transaction, psp_response) do
    Multi.new()
    |> Multi.update(:update_order, Order.changeset(order, %{status: :REFUNDED}))
    |> Multi.run(:insert_order_history, fn _repo, _changes ->
      OrderHistory.create(order, :REFUNDED)
    end)
    |> Multi.insert(
      :insert_transaction_history,
      TransactionHistory.changeset(%TransactionHistory{}, %{
        "state" => "REFUNDED",
        "transaction_id" => refund_transaction.id
      })
    )
    |> Multi.update(
      :update_refund_transaction,
      RefundTransaction.changeset(refund_transaction, %{
        status: "COMPLETED",
        psp_result: psp_response
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update_order: update_order}} -> {:ok, update_order}
      {:error, operation, failed_value, _changes_so_far} -> {:error, {operation, failed_value}}
    end
  end

  defp create_refund_transaction_payload(payload, {user_id, ticket_id, psp_reference}, params, response) do
    %{
      status: "INITIATED",
      currency: @currency,
      amount: payload.amount.value,
      refund_item_id: ticket_id,
      refund_item_type: :TICKET,
      psp: "Adyen",
      psp_reference: psp_reference,
      psp_result: response,
      payment_method: payload.payment_method,
      actor_id: user_id,
      reason: Map.get(params, "reason", "REFUND TICKET"),
      comment: Map.get(params, "comment", "Initiated via API by user #{user_id}"),
      support_ticket_id: Map.get(params, "support_ticket_id", nil)
    }
  end

  defp create_refund_transaction_payload(
         amount,
         %TicketDB{id: ticket_id, order_ticket: %{order: %{seller_id: seller_id}}} = _ticket,
         user_id,
         reason
       )
       when is_binary(seller_id) do
    create_seller_refund_transaction_payload(amount, ticket_id, user_id, reason)
  end

  defp create_refund_transaction_payload(_amount, %TicketDB{id: ticket_id} = _ticket, user_id, reason) do
    create_invitation_refund_transaction_payload(ticket_id, user_id, reason)
  end

  defp create_invitation_refund_transaction_payload(ticket_id, user_id, reason) do
    %{
      status: "COMPLETED",
      currency: @currency,
      amount: 0,
      refund_item_id: ticket_id,
      refund_item_type: :TICKET,
      psp: "Adyen",
      payment_method: "Adyen",
      psp_reference: "Adyen",
      actor_id: user_id,
      reason: reason,
      comment: "Initiated via API by user #{user_id}"
    }
  end

  defp create_seller_refund_transaction_payload(
         %{value: bill_total, currency: currency} = _amount,
         ticket_id,
         user_id,
         reason
       ) do
    %{
      status: "COMPLETED",
      currency: currency,
      amount: bill_total,
      refund_item_id: ticket_id,
      refund_item_type: :TICKET,
      actor_id: user_id,
      reason: reason,
      comment: "Initiated via API by user #{user_id}"
    }
  end

  defp revert_refund_transaction(
         %TicketDB{} = ticket,
         %RefundTransaction{actor_id: actor_id} = refund_transaction,
         psp_response
       ) do
    Multi.new()
    |> Multi.update(
      :update_ticket,
      TicketDB.changeset(ticket, %{status: :ACTIVE, refunded_at: nil})
    )
    |> Multi.run(:insert_ticket_history, fn _repo, _changes ->
      TicketHistory.create(ticket, :ACTIVE, actor_id, nil, nil, psp_response)
    end)
    |> Multi.insert(
      :insert_transaction_history,
      TransactionHistory.changeset(%TransactionHistory{}, %{
        "state" => "FAILED",
        "transaction_id" => refund_transaction.id
      })
    )
    |> Multi.update(
      :update_refund_transaction,
      RefundTransaction.changeset(refund_transaction, %{
        status: "FAILED",
        psp_result: psp_response
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update_ticket: updated_ticket}} -> {:ok, updated_ticket}
      {:error, operation, failed_value, _changes_so_far} -> {:error, {operation, failed_value}}
    end
  end

  defp get_refund_balance_account(:FAILURE_OR_GOODWILL_PROMOTER, event_id) do
    case EventsService.get_event_by_id(event_id) do
      {:ok, %{"balanceAccountId" => event_balance_account_id}} -> event_balance_account_id
      {:error, _} -> nil
    end
  end

  defp get_refund_balance_account(:FAILURE_STAGEDATES, _) do
    %{"stagedatesBalanceAccountId" => stagedates_balance_account_id} = Config.all_as_map()
    stagedates_balance_account_id
  end

  defp validate_fund_availability(amount, balance_account_id) do
    case Adyen.Services.BalancePlatform.get_balance_account(balance_account_id) do
      {:ok, %{"balances" => [%{"balance" => balance}]}} -> balance >= amount
      {:error, _} -> false
    end
  end

  @spec ticket_accessible?(
          ticket :: TicketDB.t(),
          conn_seller_id :: Ecto.UUID.t() | nil,
          conn :: Plug.Conn.t()
        ) ::
          boolean()
  defp ticket_accessible?(
         %TicketDB{event_id: event_id, order_ticket: %OrderTicket{order: %{seller_id: order_seller_id}}},
         conn_seller_id,
         conn
       ) do
    # Condition 1: User is the seller associated with the order
    is_seller = conn_seller_id != nil && order_seller_id == conn_seller_id

    # Condition 2: User has specific event permission (checked only if not the seller)
    is_seller || Auth.event_authorized?(conn, event_id, "order.edit")
  end

  defp ticket_accessible?(ticket, conn_seller_id, _conn) do
    Logger.error("Could not check if seller #{conn_seller_id} has access to ticket #{inspect(ticket)}.")

    false
  end

  defp filter_where_params(params) do
    Enum.reduce(params, dynamic(true), fn {key, value}, acc ->
      filter_where(acc, key, value)
    end)
  end

  defp filter_where(dynamic, :event_id, value) when not is_nil(value),
    do: dynamic([ticket: t], ^dynamic and t.event_id == ^value)

  defp filter_where(dynamic, :seller_id, value) when not is_nil(value),
    do: dynamic([order: o], ^dynamic and o.seller_id == ^value)

  defp filter_where(dynamic, :order_id, value) when not is_nil(value),
    do: dynamic([order_ticket: ot], ^dynamic and ot.order_id == ^value)

  defp filter_where(dynamic, :ticket_id, value) when not is_nil(value),
    do: dynamic([ticket: t], ^dynamic and t.id == ^value)

  defp filter_where(dynamic, _key, _value), do: dynamic

  @spec process_order_updates([%{order_ticket: %{order_id: Ecto.UUID.t()}}]) ::
          :ok | {:error, :publish_failed}
  defp process_order_updates(tickets) do
    tickets
    |> unique_order_ids()
    |> publish_order_updates()
    |> process_order_update_stream()
  end

  defp unique_order_ids(tickets) do
    tickets
    |> Enum.map(& &1.order_ticket.order_id)
    |> Enum.uniq()
  end

  defp publish_order_updates(order_ids) do
    Task.async_stream(
      order_ids,
      &OrdersPublisher.publish_order_update/1,
      max_concurrency: 5,
      ordered: false
    )
  end

  defp process_order_update_stream(stream) do
    stream
    |> Stream.map(fn
      {:ok, :ok} -> :ok
      {:ok, {:error, _}} -> :error
      {:error, _} -> :error
    end)
    |> Enum.reduce_while(:ok, fn
      :ok, acc -> {:cont, acc}
      :error, _ -> {:halt, {:error, :order_update_publish_failed}}
    end)
  end

  defp maybe_set_order_as_refunded(order_id) do
    order_id
    |> TicketDB.count_not_refunded_tickets_for_order_by_order_id()
    |> TicketDB.maybe_set_order_as_refunded(order_id)
  end
end
